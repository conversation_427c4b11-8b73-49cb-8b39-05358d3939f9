/** @type {import('tailwindcss').Config} */
const module = {};
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        'portfolio': {
          'navy': '#011f4b',      
          'dark': '#03396c',      
          'medium': '#005b96',    
          'light': '#6497b1',     
          'pale': '#b3cde0',      
        },
        'glass': {
          'bg': 'rgba(179, 205, 224, 0.08)',
          'border': 'rgba(179, 205, 224, 0.15)',
          'hover': 'rgba(179, 205, 224, 0.12)',
        }
      },
      backdropBlur: {
        'glass': '12px',
        'glass-strong': '16px',
      },
      boxShadow: {
        'glass': '0 4px 32px rgba(1, 31, 75, 0.3)',
        'glass-hover': '0 8px 40px rgba(1, 31, 75, 0.4)',
        'accent-glow': '0 4px 20px rgba(0, 91, 150, 0.3)',
        'accent-hover': '0 6px 30px rgba(0, 91, 150, 0.5)',
      },
      backgroundImage: {
        'glass-gradient': 'linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(100, 151, 177, 0.05) 100%)',
        'accent-gradient': 'linear-gradient(135deg, #005b96, #6497b1)',
        'hero-gradient': 'linear-gradient(180deg, #011f4b 0%, #03396c 100%)',
      },
      borderRadius: {
        'glass': '18px',
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'poppins': ['Poppins', 'sans-serif'],
      }
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        // Glass panel base class
        '.glass-panel': {
          background: 'rgba(179, 205, 224, 0.08)',
          border: '1px solid rgba(179, 205, 224, 0.15)',
          borderRadius: '18px',
          backdropFilter: 'blur(12px)',
          boxShadow: '0 4px 32px rgba(1, 31, 75, 0.3)',
          transition: 'all 0.3s ease',
        },
        '.glass-panel:hover': {
          background: 'rgba(179, 205, 224, 0.12)',
          boxShadow: '0 8px 40px rgba(1, 31, 75, 0.4)',
        },
        
        // Glass button variants
        '.glass-btn': {
          background: 'rgba(179, 205, 224, 0.1)',
          border: '1px solid rgba(179, 205, 224, 0.2)',
          borderRadius: '999px',
          backdropFilter: 'blur(12px)',
          padding: '0.75rem 2rem',
          fontWeight: '600',
          color: '#b3cde0',
          transition: 'all 0.2s ease',
          cursor: 'pointer',
        },
        '.glass-btn:hover': {
          background: 'rgba(179, 205, 224, 0.15)',
          boxShadow: '0 4px 20px rgba(100, 151, 177, 0.3)',
          transform: 'translateY(-2px)',
        },
        
        // Accent button
        '.accent-btn': {
          background: 'linear-gradient(135deg, #005b96, #6497b1)',
          border: 'none',
          borderRadius: '999px',
          padding: '0.75rem 2rem',
          fontWeight: '600',
          color: '#011f4b',
          boxShadow: '0 4px 20px rgba(0, 91, 150, 0.3)',
          transition: 'all 0.2s ease',
          cursor: 'pointer',
        },
        '.accent-btn:hover': {
          boxShadow: '0 6px 30px rgba(0, 91, 150, 0.5)',
          transform: 'translateY(-2px)',
        },
        
        // Hero glass card
        '.hero-glass': {
          background: 'linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(100, 151, 177, 0.05) 100%)',
          border: '1px solid rgba(179, 205, 224, 0.2)',
          borderRadius: '24px',
          backdropFilter: 'blur(16px)',
          boxShadow: '0 8px 40px rgba(1, 31, 75, 0.4)',
        },
        
        // Project card
        '.project-card': {
          background: 'rgba(179, 205, 224, 0.06)',
          border: '1px solid rgba(179, 205, 224, 0.1)',
          borderRadius: '16px',
          backdropFilter: 'blur(10px)',
          padding: '1.5rem',
          transition: 'all 0.3s ease',
        },
        '.project-card:hover': {
          background: 'rgba(179, 205, 224, 0.1)',
          border: '1px solid rgba(179, 205, 224, 0.2)',
          transform: 'translateY(-4px)',
          boxShadow: '0 12px 48px rgba(1, 31, 75, 0.3)',
        },
        
        // Text utilities
        '.text-portfolio-primary': {
          color: '#b3cde0',
        },
        '.text-portfolio-secondary': {
          color: '#6497b1',
        },
        '.text-portfolio-muted': {
          color: 'rgba(179, 205, 224, 0.7)',
        },
      }
      
      addUtilities(newUtilities)
    }
  ],
}
