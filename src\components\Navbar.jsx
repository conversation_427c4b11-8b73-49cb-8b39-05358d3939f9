import React from 'react'

function Navbar() {
  return (
    <div>
        <nav className="bg-portfolio-navy backdrop-blur-md border-b border-glass-border p-6">
        <div className="container mx-auto flex justify-between items-center">
          <a href="#" className="relative group">

            <div className="relative text-portfolio-pale text-5xl font-greatvibes font-normal px-2 py-2">
              Harshith
            </div>
          </a>

          <div className="bg-glass-bg backdrop-blur-glass border border-glass-border rounded-[15px] px-8 py-3 shadow-glass">
            <ul className="flex space-x-6 font-quicksand font-large">
              <li>
                <a href="#" className="relative group px-4 py-2 text-portfolio-pale hover:text-white transition-all duration-300">
                  <div className="absolute inset-0 bg-glass-hover backdrop-blur-glass rounded-[10px] opacity-0 group-hover:opacity-100 transition-all duration-300 scale-95 group-hover:scale-100"></div>
                  <span className="relative z-10">Home</span>
                </a>
              </li>
              <li>
                <a href="#" className="relative group px-4 py-2 text-portfolio-pale hover:text-white transition-all duration-300">
                  <div className="absolute inset-0 bg-glass-hover backdrop-blur-glass rounded-[10px] opacity-0 group-hover:opacity-100 transition-all duration-300 scale-95 group-hover:scale-100"></div>
                  <span className="relative z-10">About</span>
                </a>
              </li>
              <li>
                <a href="#" className="relative group px-4 py-2 text-portfolio-pale hover:text-white transition-all duration-300">
                  <div className="absolute inset-0 bg-glass-hover backdrop-blur-glass rounded-[10px] opacity-0 group-hover:opacity-100 transition-all duration-300 scale-95 group-hover:scale-100"></div>
                  <span className="relative z-10">Projects</span>
                </a>
              </li>
              <li>
                <a href="#" className="relative group px-4 py-2 text-portfolio-pale hover:text-white transition-all duration-300">
                  <div className="absolute inset-0 bg-glass-hover backdrop-blur-glass rounded-[10px] opacity-0 group-hover:opacity-100 transition-all duration-300 scale-95 group-hover:scale-100"></div>
                  <span className="relative z-10">Contact</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
        </nav>
    </div>
  )
}

export default Navbar